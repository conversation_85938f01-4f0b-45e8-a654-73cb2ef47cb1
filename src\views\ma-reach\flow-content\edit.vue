<template>
  <module edit>
    <template #main>
      <div class="template-body">
        <a-form ref="formRef" layout="vertical" class="left-form" :model="entity">
          <a-space direction="vertical" :size="16">
            <a-card class="general-card">
              <a-row :gutter="24">
                <a-col :span="7">
                  <a-form-item :label="t('reach.sms.communicationId')" field="id">
                    <a-input v-model="entity.id" :placeholder="t('reach.reminder.communicationId')"
                      :disabled="module.isEdit" />
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item :label="t('reach.sms.communicationName')" field="name"
                    :rules="[{ required: true, message: t('reach.reminder.communicationName') }]">
                    <a-input v-model="entity.name" :placeholder="t('reach.reminder.communicationName')" />
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item :label="t('reach.sms.communicationGroup')" field="group">
                    <a-input v-model="entity.group" :placeholder="t('reach.reminder.communicationGroup')" />
                  </a-form-item>
                </a-col>
                <a-col :span="2">
                  <a-form-item :label="t('reach.sms.status')">
                    <a-switch v-model="entity.enabled" type="round">
                      <template #checked> {{ t('global.button.enable') }} </template>
                      <template #unchecked> {{ t('global.button.disable') }} </template>
                    </a-switch>
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item :label="t('reach.sms.template')" field="channelType">
                    <a-select v-model="entity.flowNodeId" :placeholder="t('reach.reminder.touchpoint')"
                      @change="getFLowList">
                      <a-option
                        v-for="item of nodeConfigs"
                        :key="item.id"
                        :value="item.id"
                        >{{ item.name }}</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item v-if="entity.flowNodeId" field="channelType">
                    <div class="m-t-p">
                      <a-select v-model="entity.flowTemplateId" allow-search :placeholder="t('reach.reminder.template')"
                        @change="changeTemplate">
                        <a-option v-for="item in templateList" :key="item.id" :value="item.id"
                          :label="item.name + ' [' + item.id + ']'" />
                      </a-select>
                    </div>
                  </a-form-item>
                </a-col>
                <a-col :span="7" v-if="nodeTemplateType === 'WECHAT_CUSTOMER_MSG'">
                  <a-form-item :field="'reachChannel'" label="触达渠道" :rules="[{ required: true, message: '请选择触达渠道' }]" >
                    
                      <a-select v-model="entity.reachChannel" placeholder="请选择触达渠道" @change="onReachChannelChange">
                        <a-option v-for="item in reachChannelOptions" :key="item" :value="item" :label="item" />
                      </a-select>
                    
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <!-- <a-col v-if="templateType.value !== 'TENCENT_WORK'" :span="24"> -->
                  <a-form-item :label="t('reach.sms.templateContent')" field="setting.template">
                    <a-textarea v-model="entity.content"  :auto-size="{ minRows: 2, maxRows: 10 }" />
                  </a-form-item>

                  <!-- 企业微信消息类型：根据模板的多种pushType显示对应表单 -->
                  <template v-if="nodeTemplateType === 'TENCENT_WORK' && selectedPushTypes.length > 0">
                    <!-- 图片类型 -->
                    <template v-if="selectedPushTypes.includes('image')">
                      <a-row :gutter="24">
                        <a-col :span="12">
                          <a-form-item label="图片" :field="'imageId'" :rules="[{ required: true, message: '请选择图片' }]">
                            <a-select v-model="entity.imageId" allow-search placeholder="请选择图片" @focus="loadImageMaterials">
                              <a-option 
                                v-for="it in imageMaterials" 
                                :key="it.id" 
                                :value="it.id" 
                                :label="it.name"
                                :disabled="!['jpg','png'].includes((it.name.split('.').pop() || '').toLowerCase())"
                              />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="12" style="display:flex;align-items:center;">
                          <div class="m-t-p1">
                            <a-button type="text" size="mini" @click="previewSelected('image')" :disabled="!entity.imageId">预览</a-button>
                          </div>
                        </a-col>
                      </a-row>
                    </template>

                    <!-- 视频类型 -->
                    <template v-if="selectedPushTypes.includes('video')">
                      <a-row :gutter="24">
                        <a-col :span="12">
                          <a-form-item label="视频" :field="'videoId'" :rules="[{ required: true, message: '请选择视频' }]">
                            <a-select v-model="entity.videoId" allow-search placeholder="请选择视频" @focus="loadVideoMaterials">
                              <a-option
                                v-for="it in videoMaterials"
                                :key="it.id"
                                :value="it.id"
                                :label="it.name"
                                :disabled="(it.name.split('.').pop() || '').toLowerCase() !== 'mp4'"
                              />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="12" style="display:flex;align-items:center;">
                          <div class="m-t-p1">
                            <a-button type="text" size="mini" @click="previewSelected('video')" :disabled="!entity.videoId">预览</a-button>
                          </div>
                        </a-col>
                      </a-row>
                    </template>

                    <!-- 文件类型 -->
                    <template v-if="selectedPushTypes.includes('file')">
                      <a-row :gutter="24">
                        <a-col :span="12">
                          <a-form-item label="文件" :field="'fileId'" :rules="[{ required: true, message: '请选择文件' }]">
                            <a-select v-model="entity.fileId" allow-search placeholder="请选择文件" @focus="loadFileMaterials">
                              <a-option
                                v-for="it in fileMaterials"
                                :key="it.id"
                                :value="it.id"
                                :label="it.name"
                                :disabled="!['txt','pdf','doc','docx','ppt','pptx','xls','xlsx','xml','jpg','jpeg','png','bmp','gif']
                                  .includes((it.name.split('.').pop() || '').toLowerCase())"
                              />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="12" style="display:flex;align-items:center;">
                          <div class="m-t-p1">
                            <a-button type="text" size="mini" @click="previewSelected('file')" :disabled="!entity.fileId">预览</a-button>
                          </div>
                        </a-col>
                      </a-row>
                    </template>

                    <!-- 小程序类型 -->
                    <template v-if="selectedPushTypes.includes('miniprogram')">
                      <a-row :gutter="24">
                        <a-col :span="7">
                          <a-form-item label="消息标题" :field="'miniprogramTitle'" :rules="[{ required: true, message: '请输入消息标题' }]">
                            <a-input v-model="entity.miniprogramTitle" placeholder="请输入消息标题" />
                          </a-form-item>
                        </a-col>
                        <a-col :span="7">
                          <a-form-item label="appid" :field="'miniprogramAppid'" :rules="[{ required: true, message: '请输入appid' }]">
                            <a-input v-model="entity.miniprogramAppid" placeholder="请输入小程序appid" />
                          </a-form-item>
                        </a-col>
                        <a-col :span="7">
                          <a-form-item label="封面图片" :field="'miniprogramCoverId'" :rules="[{ required: true, message: '请选择封面图片' }]">
                            <a-select v-model="entity.miniprogramCoverId" allow-search placeholder="请选择封面图片" @focus="loadImageMaterials">
                              <a-option v-for="it in imageMaterials" :key="it.id" :value="it.id" :label="it.name" />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="2">
                          <div class="m-t-p1">
                            <a-button type="text" size="mini" @click="previewSelected('miniprogramCover')" :disabled="!entity.miniprogramCoverId">预览</a-button>
                          </div>
                        </a-col>
                      </a-row>
                      <a-row :gutter="24">
                        <a-col :span="24">
                          <a-form-item label="页面路径" :field="'miniprogramPagePath'" :rules="[{ required: true, message: '请输入页面路径' }]">
                            <a-input v-model="entity.miniprogramPagePath" placeholder="请输入小程序页面路径" />
                          </a-form-item>
                        </a-col> 
                      </a-row>
                    </template>

                    <!-- 链接类型 -->
                    <template v-if="selectedPushTypes.includes('link')">
                      <a-row :gutter="24">
                        <a-col :span="7">
                          <a-form-item label="链接标题" :field="'linkTitle'" :rules="[{ required: true, message: '请输入链接标题' }]">
                            <a-input v-model="entity.linkTitle" placeholder="请输入链接标题" />
                          </a-form-item>
                        </a-col>
                        <a-col :span="7">
                          <a-form-item label="链接描述" :field="'linkDescription'" >
                            <a-input v-model="entity.linkDescription" placeholder="请输入链接描述" />
                          </a-form-item>
                        </a-col>
                        <a-col :span="7">
                          <a-form-item label="封面图片" :field="'linkCoverId'" :rules="[{ required: true, message: '请选择封面图片' }]">
                            <a-select v-model="entity.linkCoverId" allow-search placeholder="请选择封面图片" @focus="loadImageMaterials">
                              <a-option v-for="it in imageMaterials" :key="it.id" :value="it.id" :label="it.name" />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="2">
                          <div class="m-t-p1">
                            <a-button type="text" size="mini" @click="previewSelected('linkCover')" :disabled="!entity.linkCoverId">预览</a-button>
                          </div>
                        </a-col>
                      </a-row>
                      <a-row :gutter="24">
                        <a-col :span="24">
                          <a-form-item label="链接URL" :field="'linkUrl'" :rules="[{ required: true, message: '请输入链接URL' }]">
                            <a-input v-model="entity.linkUrl" placeholder="请输入链接URL" />
                          </a-form-item>
                        </a-col>
                      </a-row>
                    </template>
                  </template>

                  <!-- 微信群发消息类型：保持原有逻辑 -->
                  <template v-else>
                    <!-- 图片类型：选择图片 + 图片标题 + 推荐语 + 已选图片列表 -->
                    <template v-if="selectedPushType === 'image'">
                      <a-row :gutter="24">
                        <a-col :span="8">
                          <a-form-item :field="'imageIds'" label="图片" :rules="[{ required: true, message: '请选择图片' }]">
                            <a-select 
                              v-model="entity.imageIds" 
                              multiple 
                              allow-search 
                              placeholder="请选择图片" 
                              @focus="loadImageMaterials"
                              @change="onImageSelectChange"
                            >
                              <a-option 
                                v-for="it in imageMaterials" 
                                :key="it.id" 
                                :value="it.id" 
                                :label="it.name" 
                                :disabled="isImageOptionDisabled(it.id)"
                              />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="8">
                          <a-form-item label="图片标题" field="imageTitle">
                            <a-input v-model="entity.imageTitle" placeholder="请输入图片标题" />
                          </a-form-item>
                        </a-col>
                        <a-col :span="8">
                          <a-form-item label="推荐语" field="recommendText">
                            <a-input v-model="entity.recommendText" placeholder="请输入推荐语" />
                          </a-form-item>
                        </a-col>
                      </a-row>
                      <a-form-item label="已选图片">
                        <div>
                          <div v-if="selectedImages.length === 0" style="color:#999;">暂无已选图片</div>
                          <div v-else>
                            <div v-for="img in selectedImages" :key="img.id" style="display:flex; align-items:center; height:32px;">
                              <span style="flex:1;">{{ img.name }}</span>
                              <a-button type="text" size="mini" @click="previewUrl(img.url)">预览</a-button>
                              <a-button type="text" size="mini" status="danger" @click="removeImage(img.id)">删除</a-button>
                            </div>
                          </div>
                        </div>
                      </a-form-item>
                    </template>

                    <!-- 音频类型：选择音频 + 预览按钮 -->
                    <template v-if="selectedPushType === 'audio'">
                      <a-row :gutter="24">
                        <a-col :span="12">
                          <a-form-item label="音频" :field="'audioId'" :rules="[{ required: true, message: '请选择音频' }]">
                            <a-select v-model="entity.audioId" allow-search placeholder="请选择音频" @focus="loadAudioMaterials">
                              <a-option v-for="it in audioMaterials" :key="it.id" :value="it.id" :label="it.name" />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="12" style="display:flex;align-items:center;">
                          <!-- <a-button @click="previewSelected('audio')">预览</a-button> -->
                          <div class="m-t-p1">
                          <a-button type="text" size="mini" @click="previewSelected('audio')" :disabled="!entity.audioId">预览</a-button>
                          </div>
                        </a-col>
                      </a-row>
                    </template>

                    <!-- 视频类型：选择视频 + 预览按钮 -->
                    <template v-if="selectedPushType === 'video'">
                      <a-row :gutter="24">
                        <a-col :span="12">
                          <a-form-item label="视频" :field="'videoId'" :rules="[{ required: true, message: '请选择视频' }]">
                            <a-select v-model="entity.videoId" allow-search placeholder="请选择视频" @focus="loadVideoMaterials">
                              <a-option v-for="it in videoMaterials" :key="it.id" :value="it.id" :label="it.name" />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="12" >
                          <!-- <a-button @click="previewSelected('video')">预览</a-button> -->
                           <div class="m-t-p1">
                          <a-button type="text" size="mini" @click="previewSelected('video')" :disabled="!entity.videoId">预览</a-button>
                           </div>
                        </a-col>
                      </a-row>
                    </template>

                    <!-- 图文类型：选择图文 + 允许转载 -->
                    <template v-if="selectedPushType === 'article'">
                      <a-row :gutter="24">
                        <a-col :span="12">
                          <a-form-item label="图文" :field="'articleId'" :rules="[{ required: true, message: '请选择图文' }]">
                            <a-select v-model="entity.articleId" allow-search placeholder="请选择图文" @focus="loadArticleList">
                              <a-option v-for="it in articleList" :key="it.id" :value="it.id" :label="it.name" />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="12">
                          <a-form-item label="允许转载" field="send_ignore_reprint">
                            <a-radio-group v-model="entity.send_ignore_reprint">
                              <a-radio :value="true">是</a-radio>
                              <a-radio :value="false">否</a-radio>
                            </a-radio-group>
                          </a-form-item>
                        </a-col>
                      </a-row>
                    </template>

                    <!-- 卡券类型：选择卡券 -->
                    <template v-if="selectedPushType === 'coupon'">
                      <a-form-item label="卡券" :field="'couponId'" :rules="[{ required: true, message: '请选择卡券' }]">
                        <a-select v-model="entity.couponId" allow-search placeholder="请选择卡券" @focus="loadCouponList">
                          <a-option v-for="it in couponList" :key="it.id" :value="it.id" :label="it.name" />
                        </a-select>
                      </a-form-item>
                    </template>
                  </template>
                  <a-form-item :label="t('reach.sms.description')" field="summary">
                    <a-textarea v-model="entity.summary" :placeholder="t('reach.reminder.description')" />
                  </a-form-item>
                </a-col>
                <!-- <a-col :span="24">
                  <a-typography-title :heading="6">
                    {{ t('reach.sms.fieldMappingConfiguration') }}
                    <a-tooltip :content="t('reach.sms.syncTemplateField')">
                      <icon-sync class="sync-fields" @click="refreshFields" />
                    </a-tooltip>
                  </a-typography-title>
                  <MappingTemplate v-model:dataList="entity.mapping" :is-change="true"
                    :customer-model="customerModel" />
                </a-col> -->
              </a-row>
            </a-card>
          </a-space>
        </a-form>

        <!-- 显示 -->
        <!-- <ModelView :entity="entity" :view-type="viewType.value" /> -->
      </div>
    </template>

    <template #action>
      <!-- <a-button v-if="module.isEdit" type="primary" :loading="simulating" @click="simulate">模拟发送</a-button> -->
      <simulate-dialog ref="simulateDlg" />
    </template>
  </module>
</template>

<script>
import { ref, provide, computed, getCurrentInstance, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getMaterialList } from "@/api/material";
import { findBenefitList } from "@/api/benefit";
import { createContent, modifyContent, getContent } from "@/api/flow-content";
import { Message } from "@arco-design/web-vue";
import { useMenuItemState } from "@/store";
import { formatFields } from "@/utils/field";
import { getDynamicFields } from "@/utils/content";
import { findCustomerModel } from "@/api/system";
import { findTemplateList } from "@/api/flow";
import { findNodeLists } from "@/api/node";
import { findNodeList } from "@/api/node";
import SimulateDialog from "@/components/modal-dlg/customer-selector-dlg.vue";
import MappingTemplate from "@/components/ma/mapping-template/index.vue";
import ModelView from "@/components/ma/model-view/index.vue";
// 从api/draft导入getDraftInfo
import { getDraftInfo } from "@/api/draft";

export default {
  components: {
    SimulateDialog,
    MappingTemplate,
    ModelView,
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();
    const route = useRoute();
    const router = useRouter();
    const menuItem = useMenuItemState();
    menuItem.loadFromLocalStorage();
    const queryValue = route.query.id;
    const customerModel = ref({});
    const viewType = ref("SMS");

    const module = ref({
      entityIdField: "id",
      mainPath: menuItem.path,
      breadcrumb: [
        {
          // name: `${menuItem.meta.title || ""}沟通列表`,
          name: `${menuItem.meta.title || ''} ${t("reach.title.communicationList")}`,
          path: menuItem.path,
        },
        {
          // name: `编辑${menuItem.meta.title}`,
          name: `${t("global.button.edit")} ${menuItem.meta.title}`,
        },
      ],
      isEdit: !!queryValue,
    });

    const loading = ref(false);
    const entity = ref({
      setting: {},
      mapping: [],
      content: "",
      category: menuItem.meta.id,
      reachChannel: "",
      allowReprint: false,
      send_ignore_reprint: true,
      // 企业微信消息相关字段
      imageId: "",
      videoId: "",
      fileId: "",
      miniprogramTitle: "",
      miniprogramAppid: "",
      miniprogramCoverId: "",
      miniprogramPagePath: "",
      linkTitle: "",
      linkDescription: "",
      linkCoverId: "",
      linkUrl: "",
      // 微信群发消息相关字段
      imageIds: [],
      imageTitle: "",
      recommendText: "",
      audioId: "",
      articleId: "",
      couponId: "",
    });
    const formRef = ref({});
    const simulating = ref(false);

    const flows = ref([]);
    const starts = ref([]);
    const nodeList = ref([]);
    const nodeConfigs = ref([]);
    const templateList = ref([]);
    const templateType = ref();
    const reachChannelOptions = ref([
      "环游嘉年华公众号",
      "华立电竞",
      "卡片嘉年华",
      "太鼓鼓众广场",
      "华立科技",
    ]);

    // 物料/卡券等下拉数据
    const imageMaterials = ref([]);
    const audioMaterials = ref([]);
    const videoMaterials = ref([]);
    const fileMaterials = ref([]);
    const articleList = ref([]);
    const couponList = ref([]);

    viewType.value = computed(() => {
      const item = nodeList.value.find((x) => {
        return x.id === entity.value.flowNodeId;
      });
      return item?.setting?.templateType || "SMS";
    });
    templateType.value = computed(() => {
      const item = nodeList.value[0]
      return item.setting.templateType;
    });
    const getFLowList = (flowNodeId = "", type = "change") => {
      const params = {};
      if (flowNodeId) {
        params.expression = `nodeConfigId eq ${flowNodeId}`;
      }

      findTemplateList(params).then((res) => {
        if (type === "change") {
          entity.value.flowTemplateId = "";
          entity.value.content = "";
          entity.value.mapping = [];
          entity.value.reachChannel = "";
        }
        const list = Array.isArray(res) ? res : [];
        // 基于contentTypes字段为模板打上类型标记，便于前端联动
        const mapWithType = list.map((it) => {
          const contentTypes = it?.contentTypes || [];
          let pushType = it?.pushType || it?.type || "";
          
          // 如果没有pushType，则根据contentTypes确定
          if (!pushType && contentTypes.length > 0) {
            if (contentTypes.includes('coupon')) pushType = "coupon";
            else if (contentTypes.includes('article')) pushType = "article";
            else if (contentTypes.includes('image')) pushType = "image";
            else if (contentTypes.includes('audio')) pushType = "audio";
            else if (contentTypes.includes('video')) pushType = "video";
          }
          
          return { ...it, pushType };
        });
        // 若后端暂无数据，提供前端演示用的占位模板
        templateList.value = mapWithType.length > 0 ? mapWithType : [
          { id: 'tpl_img_demo', name: '图片消息-活动通知 [demo_img]', pushType: 'image', content: '' },
          { id: 'tpl_audio_demo', name: '音频消息-活动通知 [demo_audio]', pushType: 'audio', content: '' },
          { id: 'tpl_video_demo', name: '视频消息-活动通知 [demo_video]', pushType: 'video', content: '' },
          { id: 'tpl_article_demo', name: '图文消息-活动通知 [demo_article]', pushType: 'article', content: '' },
          { id: 'tpl_coupon_demo', name: '卡券消息-活动通知 [demo_coupon]', pushType: 'coupon', content: '' },
        ];

        // 查询当前模板
        changeTemplate(entity.value.flowTemplateId, "init");
      });
    };

    const changeTemplate = (id, type = "change") => {
      const template = templateList.value.find((x) => {
        return x.id === id;
      });

      if (!template) {
        return false;
      }
      entity.value.content = template.content;
      
      // 企业微信消息：支持多种contentTypes
      if (nodeTemplateType.value === 'TENCENT_WORK') {
        // 基于contentTypes字段识别包含的多种类型
        const contentTypes = template?.contentTypes || [];
        selectedPushTypes.value = [...contentTypes];
      } else {
        // 其他消息类型：保持原有单一pushType逻辑
        let pushType = template?.pushType || template?.type || '';
        
        // 如果没有pushType，则根据contentTypes确定
        if (!pushType && template?.contentTypes && template.contentTypes.length > 0) {
          const contentTypes = template.contentTypes;
          // 优先级：coupon > article > image > audio > video
          if (contentTypes.includes('coupon')) pushType = 'coupon';
          else if (contentTypes.includes('article')) pushType = 'article';
          else if (contentTypes.includes('image')) pushType = 'image';
          else if (contentTypes.includes('audio')) pushType = 'audio';
          else if (contentTypes.includes('video')) pushType = 'video';
        }
        
        selectedPushType.value = pushType;
        
        // 当选择图文类型时，默认允许转载为"是"
        if (pushType === 'article') {
          entity.value.allowReprint = true;
        }
      }
      
      if (template?.variables && template.variables.length > 0) {
        let content = "";
        template.variables.forEach((item) => {
          if (item.type === "image") {
            content += `<img style="width: 100%" src="${item.value}">`;
          }
          if (item.type === "text") {
            content += `<div class="content-text">${item.value}</div>`;
          }
        });
        entity.value.viewContent = content;
      } else {
        entity.value.viewContent = template.content;
      }

      if (entity.value.mapping.length === 0 || type === "change") {
        refreshFields();
      }
    };

    const refreshFields = () => {
      entity.value.mapping.length = 0;
      entity.value.mapping.push(...getDynamicFields(entity.value.content));
    };

    const getNodeConfigList = async () => {
      const res = await findNodeList();
      nodeConfigs.value = []; // 清空数组避免重复数据
      res.map((item) => {
        if (item.category !== "SYSTEM") {
          nodeConfigs.value.push({
            id: item.id,
            name: item.name,
            setting: item.setting,
          });
        }
        return [];
      });
    };

    const bindData = async () => {
      if (module.value.isEdit) {
        const res = await getContent(queryValue);
        entity.value = {
          ...entity.value,
          ...res,
          mapping: res.mapping || [],
          allowReprint: res.allowReprint || false,
          // 保持从后端获取的send_ignore_reprint值，如果没有则默认为true
          send_ignore_reprint: res.send_ignore_reprint !== undefined ? res.send_ignore_reprint : true,
          // 确保短信模板的content字段填入模板内容输入框
          content: res.content || "",
          // 企业微信消息相关字段
          imageId: res.imageId || "",
          videoId: res.videoId || "",
          fileId: res.fileId || "",
          miniprogramTitle: res.miniprogramTitle || "",
          miniprogramAppid: res.miniprogramAppid || "",
          miniprogramCoverId: res.miniprogramCoverId || "",
          miniprogramPagePath: res.miniprogramPagePath || "",
          linkTitle: res.linkTitle || "",
          linkDescription: res.linkDescription || "",
          linkCoverId: res.linkCoverId || "",
          linkUrl: res.linkUrl || "",
          // 微信群发消息相关字段
          imageIds: res.imageIds || [],
          imageTitle: res.imageTitle || "",
          recommendText: res.recommendText || "",
          audioId: res.audioId || "",
          articleId: res.articleId || "",
          couponId: res.couponId || "",
        };
        
        // 从extra中获取reachChannel值
        if (res.extra) {
          const extraData = typeof res.extra === 'string' ? JSON.parse(res.extra) : res.extra;
          if (extraData.reachChannel) {
            entity.value.reachChannel = extraData.reachChannel;
          }
          // 从extra中恢复send_ignore_reprint值
          if (extraData.send_ignore_reprint !== undefined) {
            // 将字符串格式的布尔值或数字转换为布尔类型
            const value = extraData.send_ignore_reprint;
            entity.value.send_ignore_reprint = value === 'true' || value === true || value === '1' || value === 1;
          }
        }
      }

      const data = await findCustomerModel();
      if (data) {
        customerModel.value = data;
        customerModel.value.fields = formatFields(data.fields);
      }

      const params = {};
      if (menuItem.meta.id) {
        params.expression = `flowCategory eq ${menuItem.meta.id}`;
      }

      nodeList.value = await findNodeLists(params);
      await getNodeConfigList();
      
      // 在获取节点配置后再恢复素材信息
      if (module.value.isEdit && entity.value.extra) {
        await restoreMaterialsFromExtra();
      }
      
      await getFLowList(entity.value.flowNodeId, "init");

      // entity.value.mapping = ;
    };


    const simulate = async () => {
      const customers = await simulateDlg.value.show(customerModel.value);
      if (customers) {
        simulating.value = true;
        try {
          const customer = customers[0];
          const simResult = {};
          // await simulateCommunicateSend({
          //   capabilityId: entity.value.id,
          //   customerId: customer,
          //   triggerType: 'simulate',
          //   setting: {
          //     limited: false
          //   },
          //   budgetSetting: {
          //     enabled: false
          //   },
          // });
          if (simResult.status !== "SUCCESS") {
            Message.error({
              content: `客户${simResult.customerId}模拟失败：${simResult.message}`,
              closable: true
            });
            return;
          }
          Message.success("模拟发送成功！");
        } finally {
          simulating.value = false;
        }
      }
    };

    const save = async () => {
      try {
        const result = await formRef.value.validate();
        if (result) {
          console.log('表单校验未通过:', result);
          return; // 校验失败时直接返回，不执行保存操作
        }
      } catch (error) {
        console.log('表单校验未通过:', error);
        return; // 校验失败时直接返回，不执行保存操作
      }
      
      // 创建一个保存用的实体副本
      const saveEntity = { ...entity.value };
      
      // 当模板类型为企业微信消息时，将素材信息保存到extra字段
      if (nodeTemplateType.value === 'TENCENT_WORK') {
        const extra = {};
        
        // 图片素材
        if (entity.value.imageId) {
          const material = imageMaterials.value.find(m => m.id === entity.value.imageId);
          extra.image = [{
            source_url: material?.url || "",
            source_name: material?.name || "",
            sort: 0
          }];
        }
        
        // 链接素材
        if (entity.value.linkTitle || entity.value.linkUrl) {
          const linkCover = imageMaterials.value.find(m => m.id === entity.value.linkCoverId);
          extra.link = [{
            title: entity.value.linkTitle || "",
            desc: entity.value.linkDescription || "",
            url: entity.value.linkUrl || "",
            picurl: linkCover?.url || "",
            sort: 0
          }];
        }
        
        // 小程序素材
        if (entity.value.miniprogramTitle || entity.value.miniprogramAppid) {
          const miniprogramCover = imageMaterials.value.find(m => m.id === entity.value.miniprogramCoverId);
          extra.miniprogram = [{
            title: entity.value.miniprogramTitle || "",
            appid: entity.value.miniprogramAppid || "",
            page: entity.value.miniprogramPagePath || "",
            sourc_url: miniprogramCover?.url || "",
            source_name: miniprogramCover?.name || "",
            sort: 0
          }];
        }
        
        // 视频素材
        if (entity.value.videoId) {
          const videoMaterial = videoMaterials.value.find(m => m.id === entity.value.videoId);
          extra.video = [{
            source_url: videoMaterial?.url || "",
            source_name: videoMaterial?.name || "",
            sort: 0
          }];
        }
        
        // 文件素材
        if (entity.value.fileId) {
          const fileMaterial = fileMaterials.value.find(m => m.id === entity.value.fileId);
          extra.file = [{
            source_url: fileMaterial?.url || "",
            source_name: fileMaterial?.name || "",
            sort: 0
          }];
        }
        
        // 将extra中的数组转换为字符串
        const stringifiedExtra = {};
        Object.keys(extra).forEach(key => {
          if (Array.isArray(extra[key])) {
            stringifiedExtra[key] = JSON.stringify(extra[key]);
          } else {
            stringifiedExtra[key] = extra[key];
          }
        });
        
        saveEntity.extra = stringifiedExtra;
        
        // 移除不需要的字段
        delete saveEntity.imageId;
        delete saveEntity.linkCoverId;
        delete saveEntity.linkDescription;
        delete saveEntity.linkTitle;
        delete saveEntity.linkUrl;
        delete saveEntity.miniprogramAppid;
        delete saveEntity.miniprogramCoverId;
        delete saveEntity.miniprogramPagePath;
        delete saveEntity.miniprogramTitle;
        delete saveEntity.videoId;
        delete saveEntity.fileId;
        delete saveEntity.reachChannel;
      } else if (nodeTemplateType.value === 'WECHAT_CUSTOMER_MSG') {
        // 当模板类型为微信群发消息时，将素材信息保存到extra字段
        const extra = {};
        
        // 添加触达渠道编码映射
        const reachChannelMap = {
          '环游嘉年华公众号': 'funLoopLand',
          '华立电竞': 'esports',
          '卡片嘉年华': 'card',
          '太鼓鼓众广场': 'taiko',
          '华立科技': 'wahlap'
        };
        
        // 添加编码格式的触达渠道
        if (entity.value.reachChannel) {
          extra.reachChannel = reachChannelMap[entity.value.reachChannel] || entity.value.reachChannel;
        }
        
        // 只有当选择的素材类型为图文时才添加send_ignore_reprint字段
        if (selectedPushType.value === 'article') {
          extra.send_ignore_reprint = entity.value.send_ignore_reprint ? "1" : "0";
        }
        
        // 图片素材（微信群发消息用imageIds数组）- 修改为新的images结构
        if (entity.value.imageIds && entity.value.imageIds.length > 0) {
          const imageList = entity.value.imageIds.map((id, index) => {
            const material = imageMaterials.value.find(m => m.id === id);
            return {
              source_url: material?.url || "",
              source_name: material?.name || "",
              sort: index
            };
          });
          // 构建新的images结构
          extra.images = [{
            media_ids: imageList,
            recommend: entity.value.recommendText || "",
            title: entity.value.imageTitle || "",
            sort: 0
          }];
        }
        
        // 音频素材
        if (entity.value.audioId) {
          const material = audioMaterials.value.find(m => m.id === entity.value.audioId);
          extra.voice = [{
            source_url: material?.url || "",
            source_name: material?.name || "",
            sort: 0
          }];
        }
        
        // 视频素材
        if (entity.value.videoId) {
          const videoMaterial = videoMaterials.value.find(m => m.id === entity.value.videoId);
          extra.mpvideo = [{
            media_id: videoMaterial?.mediaId || "",
            title: videoMaterial?.title || "",
            description: videoMaterial?.description || "",
            source_url: videoMaterial?.url || "",
            source_name: videoMaterial?.name || "",
            sort: 0
          }];
        }
        
        // 图文素材
        if (entity.value.articleId) {
          extra.mpnews = [{
            media_id: entity.value.articleId,
            sort: 0
          }];
        }
        
        // 卡券素材
        if (entity.value.couponId) {
          extra.wxcard = [{
            card_id: entity.value.couponId,
            sort: 0
          }];
        }
        
        // 将extra中的数组转换为字符串
        const stringifiedExtra = {};
        Object.keys(extra).forEach(key => {
          if (Array.isArray(extra[key])) {
            stringifiedExtra[key] = JSON.stringify(extra[key]);
          } else {
            stringifiedExtra[key] = extra[key];
          }
        });
        
        saveEntity.extra = stringifiedExtra;
        
        // 移除不需要的字段
        delete saveEntity.imageIds;
        delete saveEntity.imageTitle;
        delete saveEntity.recommendText;
        delete saveEntity.audioId;
        delete saveEntity.videoId;
        delete saveEntity.articleId;
        delete saveEntity.allowReprint;
        delete saveEntity.couponId;
        delete saveEntity.reachChannel;
        delete saveEntity.send_ignore_reprint;
      } else {
        // 其他类型的消息，如果存在reachChannel，则将其包装到extra字段中
        // if (saveEntity.reachChannel) {
        //   // 添加触达渠道编码映射
        //   const reachChannelMap = {
        //     '环游嘉年华公众号': 'funLoopLand',
        //     '华立电竞': 'esports',
        //     '卡片嘉年华': 'card',
        //     '太鼓鼓众广场': 'taiko',
        //     '华立科技': 'wahlap'
        //   };
          
        //   saveEntity.extra = {
        //     reachChannel: reachChannelMap[saveEntity.reachChannel] || saveEntity.reachChannel
        //   };
        //   delete saveEntity.reachChannel;
        // }
        delete saveEntity.imageIds;
        delete saveEntity.imageTitle;
        delete saveEntity.recommendText;
        delete saveEntity.audioId;
        delete saveEntity.videoId;
        delete saveEntity.articleId;
        delete saveEntity.allowReprint;
        delete saveEntity.couponId;
        delete saveEntity.reachChannel;
        delete saveEntity.imageId;
        delete saveEntity.linkCoverId;
        delete saveEntity.linkDescription;
        delete saveEntity.linkTitle;
        delete saveEntity.linkUrl;
        delete saveEntity.miniprogramAppid;
        delete saveEntity.miniprogramCoverId;
        delete saveEntity.miniprogramPagePath;
        delete saveEntity.miniprogramTitle;
        delete saveEntity.videoId;
        delete saveEntity.fileId;
      }
      
      // 移除send_ignore_reprint字段，因为已在微信群发消息的逻辑中处理
      delete saveEntity.send_ignore_reprint;
      
      if (module.value.isEdit) {
        await modifyContent(saveEntity);
      } else {
        await createContent(saveEntity);
      }

      Message.success(t('global.tips.success.save'));
      router.push(menuItem.path); // 保存成功后重定向回主页面
    };

    // const isWeChatMassSelected = computed(() => {
    //   return nodeTemplateType.value === 'WECHAT_CUSTOMER_MSG';
    // });

    const selectedPushType = ref("");
    const selectedPushTypes = ref([]); // 企业微信消息支持多种类型
    
    // 计算当前选择节点的模板类型
    const nodeTemplateType = computed(() => {
      const selectedNode = nodeConfigs.value.find(node => node.id === entity.value.flowNodeId);
      return selectedNode?.setting?.templateType;
    });
    const selectedImages = computed(() => {
      if (!entity.value.imageIds || !Array.isArray(entity.value.imageIds)) return [];
      return imageMaterials.value.filter((it) => entity.value.imageIds.includes(it.id));
    });

    const loadImageMaterials = async () => {
      if (imageMaterials.value.length > 0) return;
      const res = await getMaterialList({ page: 1, size: 50, type: 'image' });
      imageMaterials.value = res?.data?.list || res?.list || [];
    };
    const loadAudioMaterials = async () => {
      if (audioMaterials.value.length > 0) return;
      const res = await getMaterialList({ page: 1, size: 50, type: 'audio' });
      audioMaterials.value = res?.data?.list || res?.list || [];
    };
    const loadVideoMaterials = async () => {
      if (videoMaterials.value.length > 0) return;
      const res = await getMaterialList({ page: 1, size: 50, type: 'video' });
      videoMaterials.value = res?.data?.list || res?.list || [];
    };
    const loadFileMaterials = async () => {
      if (fileMaterials.value.length > 0) return;
      const res = await getMaterialList({ page: 1, size: 50, type: 'file' });
      fileMaterials.value = res?.data?.list || res?.list || [];
    };
    const loadArticleList = async () => {
      // 如果已加载过数据则直接返回
      if (articleList.value.length > 0) return;
      
      // 根据触达渠道获取对应的wechatOfficialAccountCode
      let wechatOfficialAccountCode = '';
      switch(entity.value.reachChannel) {
        case '环游嘉年华公众号':
          wechatOfficialAccountCode = 'funLoopLand';
          break;
        case '华立电竞':
          wechatOfficialAccountCode = 'esports';
          break;
        case '卡片嘉年华':
          wechatOfficialAccountCode = 'card';
          break;
        case '太鼓鼓众广场':
          wechatOfficialAccountCode = 'taiko';
          break;
        case '华立科技':
          wechatOfficialAccountCode = 'wahlap';
          break;
        default:
          wechatOfficialAccountCode = '';
      }
      
      // 如果没有选择触达渠道，则不请求数据
      if (!wechatOfficialAccountCode) {
        Message.warning('请先选择触达渠道');
        return;
      }
      
      try {
        // 添加调试信息
        // console.log('请求参数:', { 
        //   wechatOfficialAccountCode,
        //   offset: 0,
        //   count: 10
        // });
        
        // 调用获取草稿信息接口，添加默认参数offset和count
        const res = await getDraftInfo({ 
          wechatOfficialAccountCode,
          offset: 0,
          count: 10
        });
        
        // 添加响应调试信息
        // console.log('接口响应原始数据:', res);
        // console.log('接口响应数据结构:', typeof res, res instanceof Object ? Object.keys(res) : '非对象');
        // console.log('接口响应data:', res?.data);
        // console.log('接口响应item:', res?.data?.item);
        
        // 处理返回的数据，转换为下拉选项格式
        // 注意：axios拦截器已经返回了response.data，所以res就是DraftInfoResponse
        // 从res.data.item中提取数据
        articleList.value = res?.data?.item?.map(item => ({
          id: item.media_id,
          name: item.content?.news_item?.[0]?.title || item.title
        })) || [];
        
        // 添加处理后数据调试信息
        // console.log('处理后的articleList:', articleList.value);
      } catch (error) {
        console.error('获取图文列表失败:', error);
        // 添加详细错误信息
        console.error('错误详情:', {
          message: error?.message,
          stack: error?.stack,
          response: error?.response,
          request: error?.request
        });

        // 根据错误类型显示不同的错误信息
        let errorMessage = '获取图文列表失败';
        if (error?.message) {
          if (error.message.includes('token')) {
            errorMessage = '身份验证失败，请重试';
          } else if (error.message.includes('网络')) {
            errorMessage = '网络连接失败，请检查网络';
          } else if (error.message.includes('服务器')) {
            errorMessage = '服务器错误，请稍后重试';
          }
        }

        Message.error(errorMessage);
      }
    };
    
    // 触达渠道变更时清空已选图文
    const onReachChannelChange = () => {
      entity.value.articleId = '';
      // 清空articleList以便重新加载
      articleList.value = [];
    };
    
    const loadCouponList = async () => {
      if (couponList.value.length > 0) return;
      const res = await findBenefitList({ fields: 'name', expression: 'type eq coupon AND status eq ENABLED' });
      couponList.value = res || [];
    };

    const previewSelected = (kind) => {
      let item;
      if (kind === 'audio') item = audioMaterials.value.find((x) => x.id === entity.value.audioId);
      if (kind === 'video') item = videoMaterials.value.find((x) => x.id === entity.value.videoId);
      if (kind === 'image') item = imageMaterials.value.find((x) => x.id === entity.value.imageId);
      if (kind === 'file') item = fileMaterials.value.find((x) => x.id === entity.value.fileId);
      if (kind === 'miniprogramCover') item = imageMaterials.value.find((x) => x.id === entity.value.miniprogramCoverId);
      if (kind === 'linkCover') item = imageMaterials.value.find((x) => x.id === entity.value.linkCoverId);
      if (item?.url) previewUrl(item.url);
    };

    const previewUrl = (url) => {
      if (!url) return;
      window.open(url, '_blank');
    };
    const removeImage = (id) => {
      if (!Array.isArray(entity.value.imageIds)) return;
      entity.value.imageIds = entity.value.imageIds.filter((x) => x !== id);
    };

    const onImageSelectChange = (value) => {
      if (value && value.length > 5) {
        // 保留前5个选项
        entity.value.imageIds = value.slice(0, 5);
        Message.warning("最多选择5张图片，请取消勾选已添加的图片。");
      }
    };

    const isImageOptionDisabled = (imageId) => {
      // 如果已选择的图片数量达到5张，且当前图片未被选中，则禁用该选项
      return entity.value.imageIds && 
             entity.value.imageIds.length >= 5 && 
             !entity.value.imageIds.includes(imageId);
    };

    // 从extra字段恢复素材信息
    const restoreMaterialsFromExtra = async () => {
      if (!entity.value.extra) return;
      
      // 如果extra是字符串，需要先解析为对象
      const extraData = typeof entity.value.extra === 'string' ? JSON.parse(entity.value.extra) : entity.value.extra;
      
      // 添加编码到中文的映射
      const reachChannelCodeMap = {
        'funLoopLand': '环游嘉年华公众号',
        'esports': '华立电竞',
        'card': '卡片嘉年华',
        'taiko': '太鼓鼓众广场',
        'wahlap': '华立科技'
      };
      
      // 获取当前节点的模板类型
      const currentNode = nodeConfigs.value.find(node => node.id === entity.value.flowNodeId);
      const currentTemplateType = currentNode?.setting?.templateType;
      
      // 恢复触达渠道
      if (extraData.reachChannel) {
        entity.value.reachChannel = reachChannelCodeMap[extraData.reachChannel] || extraData.reachChannel;
      }
      
      if (currentTemplateType === 'TENCENT_WORK') {
        // 企业微信消息的素材恢复逻辑
        // 恢复图片素材
        if (extraData.image && extraData.image.length > 0) {
          const imageData = typeof extraData.image === 'string' ? JSON.parse(extraData.image) : extraData.image;
          await loadImageMaterials();
          const img = imageData[0];
          const material = imageMaterials.value.find(m => m.url === img.source_url || m.name === img.source_name);
          if (material) {
            entity.value.imageId = material.id;
          }
        }
        
        // 恢复链接素材
        if (extraData.link && extraData.link.length > 0) {
          const linkData = typeof extraData.link === 'string' ? JSON.parse(extraData.link) : extraData.link;
          const link = linkData[0];
          entity.value.linkTitle = link.title || "";
          entity.value.linkDescription = link.desc || "";
          entity.value.linkUrl = link.url || "";
          if (link.picurl) {
            await loadImageMaterials();
            const coverMaterial = imageMaterials.value.find(m => m.url === link.picurl);
            if (coverMaterial) {
              entity.value.linkCoverId = coverMaterial.id;
            }
          }
        }
        
        // 恢复小程序素材
        if (extraData.miniprogram && extraData.miniprogram.length > 0) {
          const miniprogramData = typeof extraData.miniprogram === 'string' ? JSON.parse(extraData.miniprogram) : extraData.miniprogram;
          const miniprogram = miniprogramData[0];
          entity.value.miniprogramTitle = miniprogram.title || "";
          entity.value.miniprogramAppid = miniprogram.appid || "";
          entity.value.miniprogramPagePath = miniprogram.page || "";
          if (miniprogram.sourc_url) {
            await loadImageMaterials();
            const coverMaterial = imageMaterials.value.find(m => m.url === miniprogram.sourc_url || m.name === miniprogram.source_name);
            if (coverMaterial) {
              entity.value.miniprogramCoverId = coverMaterial.id;
            }
          }
        }
        
        // 恢复视频素材
        if (extraData.video && extraData.video.length > 0) {
          const videoData = typeof extraData.video === 'string' ? JSON.parse(extraData.video) : extraData.video;
          const video = videoData[0];
          await loadVideoMaterials();
          const videoMaterial = videoMaterials.value.find(m => m.url === video.source_url || m.name === video.source_name);
          if (videoMaterial) {
            entity.value.videoId = videoMaterial.id;
          }
        }
        
        // 恢复文件素材
        if (extraData.file && extraData.file.length > 0) {
          const fileData = typeof extraData.file === 'string' ? JSON.parse(extraData.file) : extraData.file;
          const file = fileData[0];
          await loadFileMaterials();
          const fileMaterial = fileMaterials.value.find(m => m.url === file.source_url || m.name === file.source_name);
          if (fileMaterial) {
            entity.value.fileId = fileMaterial.id;
          }
        }
      } else if (currentTemplateType === 'WECHAT_CUSTOMER_MSG') {
        // 微信群发消息的素材恢复逻辑
        
        // 恢复触达渠道（已提前处理）
        
        // 恢复send_ignore_reprint字段
        if (extraData.send_ignore_reprint !== undefined) {
          // 将字符串格式的布尔值或数字转换为布尔类型
          const value = extraData.send_ignore_reprint;
          entity.value.send_ignore_reprint = value === 'true' || value === true || value === '1' || value === 1;
        }
        
        // 恢复图片素材（微信群发消息用images数组）
        if (extraData.images) {
          const imagesData = typeof extraData.images === 'string' ? JSON.parse(extraData.images) : extraData.images;
          if (imagesData.length > 0) {
            const imageItem = imagesData[0];
            // 恢复推荐语和标题
            entity.value.recommendText = imageItem.recommend || "";
            entity.value.imageTitle = imageItem.title || "";
            
            // 恢复图片列表
            if (imageItem.media_ids && imageItem.media_ids.length > 0) {
              await loadImageMaterials();
              const imageIds = [];
              imageItem.media_ids.forEach(media => {
                const material = imageMaterials.value.find(m => m.url === media.source_url || m.name === media.source_name);
                if (material) {
                  imageIds.push(material.id);
                }
              });
              entity.value.imageIds = imageIds;
            }
          }
        }
        
        // 恢复音频素材
        if (extraData.voice && extraData.voice.length > 0) {
          const voiceData = typeof extraData.voice === 'string' ? JSON.parse(extraData.voice) : extraData.voice;
          const voice = voiceData[0];
          await loadAudioMaterials();
          const audioMaterial = audioMaterials.value.find(m => m.url === voice.source_url || m.name === voice.source_name);
          if (audioMaterial) {
            entity.value.audioId = audioMaterial.id;
          }
        }
        
        // 恢复视频素材
        if (extraData.mpvideo && extraData.mpvideo.length > 0) {
          const videoData = typeof extraData.mpvideo === 'string' ? JSON.parse(extraData.mpvideo) : extraData.mpvideo;
          const video = videoData[0];
          await loadVideoMaterials();
          const videoMaterial = videoMaterials.value.find(m => m.url === video.source_url || m.name === video.source_name);
          if (videoMaterial) {
            entity.value.videoId = videoMaterial.id;
          }
        }
        
        // 恢复图文素材
        if (extraData.mpnews && extraData.mpnews.length > 0) {
          const newsData = typeof extraData.mpnews === 'string' ? JSON.parse(extraData.mpnews) : extraData.mpnews;
          const news = newsData[0];
          entity.value.articleId = news.media_id;
        }
        
        // 恢复卡券素材
        if (extraData.wxcard && extraData.wxcard.length > 0) {
          const cardData = typeof extraData.wxcard === 'string' ? JSON.parse(extraData.wxcard) : extraData.wxcard;
          const card = cardData[0];
          entity.value.couponId = card.card_id;
        }
      }
    };

    const setup = {
      t,
      save,
      route,
      router,
      module,
      entity,
      formRef,
      templateList,
      bindData,
      nodeList,
      getFLowList,
      changeTemplate,
      refreshFields,
      viewType,
      customerModel,
      loading,
      flows,
      starts,
      templateType,
      reachChannelOptions,
      // isWeChatMassSelected,
      selectedPushType,
      selectedPushTypes,
      nodeTemplateType,
      imageMaterials,
      audioMaterials,
      videoMaterials,
      fileMaterials,
      articleList,
      couponList,
      selectedImages,
      loadImageMaterials,
      loadAudioMaterials,
      loadVideoMaterials,
      loadFileMaterials,
      loadArticleList,
      onReachChannelChange,
      loadCouponList,
      previewSelected,
      previewUrl,
      removeImage,
      onImageSelectChange,
      isImageOptionDisabled,
      nodeConfigs,
      getNodeConfigList,
      restoreMaterialsFromExtra,
    };
    provide("edit", setup);
    onMounted(async () => {
      await bindData();
    });
    return setup;
  },
};
</script>

<style lang="less" scoped>
.template-body {
  display: flex;
  height: calc(100vh - 160px);
  background-color: #ffffff;

  .left-form {
    overflow-x: hidden;
    overflow-y: scroll;
  }
}

::v-deep(.general-card) {
  min-height: calc(100vh - 160px);
}

.sync-fields {
  cursor: pointer;
  color: rgb(var(--primary-6));
}

.m-t-p {
  position: relative;
  width: 100%;
  margin-top: 22px;
}
.m-t-p1 {
  position: relative;
  width: 100%;
  margin-top: 35px;
}
</style>
