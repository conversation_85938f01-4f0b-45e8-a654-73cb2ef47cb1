import Mock from 'mockjs';
import setupMock, {
    successResponseWrap,
    failResponseWrap,
} from '@/utils/setup-mock';
import type { MockParams } from '@/types/mock';

// 模拟素材数据
const materialData = [
    
    
    {
        id: 1,
        name: "2025年三期NDD（注册报名）物料清单.xlsx",
        type: "附件",
        size: "2.5MB",
        createTime: "2025-07-29 09:31",
        updateTime: "2025-07-29 09:31",
        url: "http://localhost:3000/mock/files/material1.xlsx"
    },
    {
        id: 2,
        name: "2025年三期NDD（主营销）物料清单.xlsx",
        type: "附件",
        size: "2.5MB",
        createTime: "2025-07-29 09:31",
        updateTime: "2025-07-29 09:31",
        url: "/mock/files/material2.xlsx"
    },
    {
        id: 3,
        name: "B端客户营销资料汇总清单.xlsx",
        type: "附件",
        size: "1.8MB",
        createTime: "2025-07-16 10:36",
        updateTime: "2025-07-16 10:36",
        url: "/mock/files/material3.xlsx"
    },
    {
        id: 4,
        name: "营销花卉75ml.png",
        type: "图片",
        size: "856KB",
        createTime: "2025-07-08 15:53",
        updateTime: "2025-07-08 15:53",
        url: "/mock/images/flower.png",
        thumbnail: "http://localhost:3000/mock/images/flower_thumb.png"
    },
    {
        id: 5,
        name: "1456_1745477693.mp4",
        type: "视频",
        size: "12.3MB",
        createTime: "2025-07-07 18:01",
        updateTime: "2025-07-07 18:01",
        url: "/mock/videos/video1.mp4",
        thumbnail: "/mock/videos/video1_thumb.jpg",
        videoTitle: "产品介绍视频",
        videoDescription: "详细介绍公司核心产品的功能特点和使用方法"
    },
    {
        id: 6,
        name: "微信图片_20220314100523.jpg",
        type: "图片",
        size: "2.1MB",
        createTime: "2025-07-04 15:17",
        updateTime: "2025-07-04 15:17",
        url: "/mock/images/wechat_image.jpg",
        thumbnail: "/mock/images/wechat_image_thumb.jpg"
    },
    {
        id: 7,
        name: "234.txt",
        type: "附件",
        size: "1KB",
        createTime: "2025-07-04 15:17",
        updateTime: "2025-07-04 15:17",
        url: "/mock/files/234.txt"
    },
    {
        id: 8,
        name: "9wSGLSGwPGWAbCnwCTe.jpeg",
        type: "图片",
        size: "445KB",
        createTime: "2025-07-04 15:17",
        updateTime: "2025-07-04 15:17",
        url: "/mock/images/random.jpeg",
        thumbnail: "/mock/images/random_thumb.jpeg"
    },
    {
        id: 9,
        name: "234.gif",
        type: "图片",
        size: "2.8MB",
        createTime: "2025-07-04 15:17",
        updateTime: "2025-07-04 15:17",
        url: "/mock/images/234.gif",
        thumbnail: "/mock/images/234_thumb.jpg"
    },
    {
        id: 10,
        name: "01e5a34dd2383c90103770386005f9f2a_4610.mp4",
        type: "视频",
        size: "45.2MB",
        createTime: "2025-06-25 16:29",
        updateTime: "2025-06-25 16:29",
        url: "/mock/videos/video2.mp4",
        thumbnail: "/mock/videos/video2_thumb.jpg",
        videoTitle: "客户案例分享",
        videoDescription: "展示成功客户案例，分享合作经验和成果"
    },
    {
        id: 11,
        name: "IMG_7371.mp4",
        type: "视频",
        size: "128MB",
        createTime: "2025-05-23 18:24",
        updateTime: "2025-05-23 18:24",
        url: "/mock/videos/IMG_7371.mp4",
        thumbnail: "/mock/videos/IMG_7371_thumb.jpg",
        videoTitle: "活动记录视频",
        videoDescription: "公司年会活动精彩瞬间记录"
    },
    {
        id: 13,
        name: '背景音乐.mp3',
        type: '音频',
        size: '3.2MB',
        createTime: '2025-08-02 10:15',
        updateTime: '2025-08-02 10:15',
        url: 'http://localhost:3000/mock/audio/bgm.mp3'
    },
    {
        id: 12,
        name: '品牌宣传片.mp4',
        type: '视频',
        size: '89.5MB',
        createTime: '2025-08-01 14:20',
        updateTime: '2025-08-01 14:20',
        url: 'http://localhost:3000/mock/videos/promotion.mp4',
        thumbnail: 'http://localhost:3000/mock/videos/promotion_thumb.jpg',
        videoTitle: '品牌宣传片',
        videoDescription: '这是一个展示公司品牌形象和核心价值的宣传视频，时长约3分钟，包含了公司的发展历程、产品特色和企业文化。'
    },
    {
        id: 14,
        name: "夏季促销海报.jpg",
        type: "图片",
        size: "1.2MB",
        createTime: "2025-08-05 14:30",
        updateTime: "2025-08-05 14:30",
        url: "/mock/images/summer_poster.jpg",
        thumbnail: "/mock/images/summer_poster_thumb.jpg"
    },
    {
        id: 15,
        name: "产品展示图.png",
        type: "图片",
        size: "980KB",
        createTime: "2025-08-05 15:45",
        updateTime: "2025-08-05 15:45",
        url: "/mock/images/product_showcase.png",
        thumbnail: "/mock/images/product_showcase_thumb.png"
    },
];

let mockData = [...materialData];
let nextId = 12;

setupMock({
    mock: true,
    setup() {
        // 获取素材列表
        Mock.mock(new RegExp('/api/material/list'), (params: MockParams) => {
            const { page = '1', size = '20', type = '', name = '' } = params.url.split('?')[1]!
                ? Object.fromEntries(new URLSearchParams(params.url.split('?')[1]))
                : {};

            let filteredData = [...mockData];

            // 按类型过滤
            if (type && type !== 'all') {
                const typeMap: Record<string, string> = {
                    image: '图片',
                    audio: '音频',
                    video: '视频',
                    file: '附件'
                };
                if (typeMap[type]) {
                    filteredData = filteredData.filter(item => item.type === typeMap[type]);
                }
            }

            // 按名称过滤
            if (name) {
                filteredData = filteredData.filter(item =>
                    item.name?.toLowerCase()?.includes(name?.toLowerCase() || '')
                );
            }

            const total = filteredData.length;
            const pageNum = parseInt(page) || 1;
            const pageSize = parseInt(size) || 20;
            const start = (pageNum - 1) * pageSize;
            const end = start + pageSize;
            const list = filteredData.slice(start, end);

            return successResponseWrap({
                list,
                total,
                page: pageNum,
                size: pageSize
            });
        });

        // 获取单个素材详情
        Mock.mock(new RegExp('/api/material/(\\d+)'), 'get', (params: MockParams) => {
            const id = parseInt(params.url.match(/\/api\/material\/(\d+)/)?.[1] || '0');
            const material = mockData.find(item => item.id === id);

            if (!material) {
                return failResponseWrap(null, '素材不存在', 40004);
            }

            return successResponseWrap(material);
        });

        // 删除单个素材
        Mock.mock(new RegExp('/api/material/(\\d+)'), 'delete', (params: MockParams) => {
            const id = parseInt(params.url.match(/\/api\/material\/(\d+)/)?.[1] || '0');
            const index = mockData.findIndex(item => item.id === id);

            if (index === -1) {
                return failResponseWrap(null, '素材不存在', 40004);
            }

            mockData.splice(index, 1);
            return successResponseWrap(null);
        });

        // 批量删除素材
        Mock.mock(new RegExp('/api/material/batch'), 'delete', (params: MockParams) => {
            const { ids } = JSON.parse(params.body);

            if (!Array.isArray(ids) || ids.length === 0) {
                return failResponseWrap(null, '请选择要删除的素材', 40001);
            }

            mockData = mockData.filter(item => !ids.includes(item.id));
            return successResponseWrap(null);
        });

        // 上传素材
        Mock.mock(new RegExp('/api/material/upload'), 'post', () => {
            // 模拟上传成功，生成新的素材数据
            const newMaterial = {
                id: nextId++,
                name: `新上传附件_${Date.now()}.jpg`,
                // type: '图片',
                size: Mock.Random.float(100, 5000, 1, 1) + 'KB',
                createTime: Mock.Random.datetime('yyyy-MM-dd HH:mm'),
                updateTime: Mock.Random.datetime('yyyy-MM-dd HH:mm'),
                url: `http://localhost:3000/mock/files/upload_${nextId - 1}${Mock.Random.pick(['.pdf','.docx','.xlsx'])}`,
                thumbnail: '',
                type: Mock.Random.pick(['附件','图片','视频','音频'])
            };

            mockData.unshift(newMaterial);
            return successResponseWrap(newMaterial);
        });

        // 更新素材信息
        Mock.mock(new RegExp('/api/material/(\\d+)'), 'put', (params: MockParams) => {
            const id = parseInt(params.url.match(/\/api\/material\/(\d+)/)?.[1] || '0');
            const updateData = JSON.parse(params.body);
            const index = mockData.findIndex(item => item.id === id);

            if (index === -1) {
                return failResponseWrap(null, '素材不存在', 40004);
            }

            mockData[index] = { ...mockData[index], ...updateData };
            return successResponseWrap(mockData[index]);
        });
    },
});
